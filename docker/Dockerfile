FROM eclipse-temurin:21-jdk-alpine

WORKDIR /app

# 设置时区（可选）
ENV TZ=Asia/Shanghai

ENV SPRING_PROFILES_ACTIVE=dev
ENV DB_HOST=127.0.0.1
ENV DB_PORT=3306
ENV DB_USERNAME=root
ENV DB_PASSWORD=KV753t0PpVmpjd2d
ENV WX_APP_ID=wxd6978ee5ee024cc1
ENV WX_SECRET=878f56020a221bd56700da62dced5e11

# 安装证书工具
RUN apk add --no-cache ca-certificates \
    && update-ca-certificates

echo -n | openssl s_client -connect api.weixin.qq.com:443 -servername api.weixin.qq.com \
  | sed -ne '/-BEGIN CERTIFICATE-/,/-END CERTIFICATE-/p' > certificate.crt

RUN mv certificate.crt /app/cert/certificate.crt

RUN keytool -importcert -file /app/cert/certificate.crt -alias apiweixin -keystore $JAVA_HOME/lib/security/cacerts

# 复制构建阶段的 jar
COPY game-hub-server.jar app.jar

# 暴露端口
EXPOSE 8080

# 使用 exec 形式启动（更安全，可接收 SIGTERM 信号）
ENTRYPOINT ["java","-jar","/app/app.jar"]
