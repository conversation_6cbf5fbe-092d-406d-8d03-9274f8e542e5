package com.app.gamehub.config;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.api.impl.WxMaServiceImpl;
import cn.binarywang.wx.miniapp.config.impl.WxMaDefaultConfigImpl;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class WeChatConfig {

  @Value("${wechat.miniapp.appid}")
  private String appId;

  @Value("${wechat.miniapp.secret}")
  private String secret;

  @Bean
  public WxMaService wxMaService() {
    WxMaDefaultConfigImpl config = new WxMaDefaultConfigImpl();
    config.setAppid(appId);
    config.setSecret(secret);

    WxMaService service = new WxMaServiceImpl();
    service.setWxMaConfig(config);
    return service;
  }
}
