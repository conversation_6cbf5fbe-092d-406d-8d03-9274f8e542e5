package com.app.gamehub.repository;

import com.app.gamehub.entity.PositionGrab;
import com.app.gamehub.entity.PositionType;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/** 官职抢夺数据访问接口 */
@Repository
public interface PositionGrabRepository extends JpaRepository<PositionGrab, Long> {

  /** 根据王朝ID、官职类型和任职日期查找抢夺结果 */
  List<PositionGrab> findByDynastyIdAndPositionTypeAndDutyDateOrderByTimeSlotAsc(
      Long dynastyId, PositionType positionType, LocalDate dutyDate);

  /** 根据王朝ID和任职日期查找所有抢夺结果 */
  List<PositionGrab> findByDynastyIdAndDutyDateOrderByPositionTypeAscTimeSlotAsc(
      Long dynastyId, LocalDate dutyDate);

  /** 检查指定时段是否已被抢夺 */
  boolean existsByDynastyIdAndPositionTypeAndDutyDateAndTimeSlot(
      Long dynastyId, PositionType positionType, LocalDate dutyDate, Integer timeSlot);

  /** 查找指定时段的抢夺记录 */
  Optional<PositionGrab> findByDynastyIdAndPositionTypeAndDutyDateAndTimeSlot(
      Long dynastyId, PositionType positionType, LocalDate dutyDate, Integer timeSlot);

  /** 根据账号ID查找抢夺记录 */
  List<PositionGrab> findByAccountIdOrderByDutyDateDescCreatedAtDesc(Long accountId);

  /** 检查用户在指定王朝、官职类型和任职日期下是否已经抢夺了时段 */
  boolean existsByAccountIdAndDynastyIdAndPositionTypeAndDutyDate(
      Long accountId, Long dynastyId, PositionType positionType, LocalDate dutyDate);

  /** 清空指定王朝和官职类型的抢夺结果 */
  @Modifying
  @Query(
      "DELETE FROM PositionGrab pg WHERE pg.dynastyId = :dynastyId AND pg.positionType = :positionType")
  void deleteByDynastyIdAndPositionType(
      @Param("dynastyId") Long dynastyId, @Param("positionType") PositionType positionType);

  /** 清空指定王朝的所有抢夺结果 */
  @Modifying
  @Query("DELETE FROM PositionGrab pg WHERE pg.dynastyId = :dynastyId")
  void deleteByDynastyId(@Param("dynastyId") Long dynastyId);

  void deleteByAccountId(Long id);

  void deleteByDutyDateBefore(LocalDate before);
}
