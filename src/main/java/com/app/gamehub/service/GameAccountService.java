package com.app.gamehub.service;

import com.app.gamehub.dto.CreateGameAccountRequest;
import com.app.gamehub.dto.UpdateGameAccountRequest;
import com.app.gamehub.entity.Alliance;
import com.app.gamehub.entity.GameAccount;
import com.app.gamehub.exception.BusinessException;
import com.app.gamehub.repository.AllianceRepository;
import com.app.gamehub.repository.GameAccountRepository;
import com.app.gamehub.repository.UserRepository;
import com.app.gamehub.util.UserContext;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
@RequiredArgsConstructor
public class GameAccountService {
  private final GameAccountRepository gameAccountRepository;

  private final UserRepository userRepository;

  private final AllianceRepository allianceRepository;

  @Transactional
  public GameAccount createGameAccount(CreateGameAccountRequest request) {
    Long userId = UserContext.getUserId();
    // 验证用户是否存在
    if (!userRepository.existsById(userId)) {
      throw new BusinessException("用户不存在");
    }

    // 检查用户在该区是否已有2个账号
    long accountCount =
        gameAccountRepository.countByUserIdAndServerId(userId, request.getServerId());
    if (accountCount >= 2) {
      throw new BusinessException("每个用户在每个区最多只能创建2个账号");
    }

    GameAccount account = new GameAccount();
    account.setUserId(userId);
    account.setServerId(request.getServerId());
    account.setAccountName(request.getAccountName());
    account.setPowerValue(request.getPowerValue());
    account.setDamageBonus(request.getDamageBonus());
    account.setTroopLevel(request.getTroopLevel());
    account.setRallyCapacity(request.getRallyCapacity());

    return gameAccountRepository.save(account);
  }

  @Transactional
  public GameAccount updateGameAccount(Long accountId, UpdateGameAccountRequest request) {
    Long userId = UserContext.getUserId();
    GameAccount account =
        gameAccountRepository
            .findById(accountId)
            .orElseThrow(() -> new BusinessException("游戏账号不存在"));

    // 验证权限：账号所有者或盟主可以更新
    boolean canUpdate = account.getUserId().equals(userId);
    if (!canUpdate && account.getAllianceId() != null) {
      Alliance alliance = allianceRepository.findById(account.getAllianceId()).orElse(null);
      canUpdate = alliance != null && alliance.getLeaderId().equals(userId);
    }

    if (!canUpdate) {
      throw new BusinessException("没有权限更新此账号");
    }

    // 更新字段
    if (request.getAccountName() != null && !request.getAccountName().trim().isEmpty()) {
      account.setAccountName(request.getAccountName().trim());
    }
    if (request.getPowerValue() != null) {
      account.setPowerValue(request.getPowerValue());
    }
    if (request.getDamageBonus() != null) {
      account.setDamageBonus(request.getDamageBonus());
    }
    if (request.getTroopLevel() != null) {
      account.setTroopLevel(request.getTroopLevel());
    }
    if (request.getRallyCapacity() != null) {
      account.setRallyCapacity(request.getRallyCapacity());
    }
    if (request.getMemberTier() != null) {
      account.setMemberTier(request.getMemberTier());
    }

    return gameAccountRepository.save(account);
  }

  @Transactional
  public void deleteGameAccount(Long accountId) {
    Long userId = UserContext.getUserId();
    GameAccount account =
        gameAccountRepository
            .findById(accountId)
            .orElseThrow(() -> new BusinessException("游戏账号不存在"));

    // 验证是否为账号所有者
    if (!account.getUserId().equals(userId)) {
      throw new BusinessException("只能删除自己的账号");
    }

    // 如果账号已加入联盟，需要先退出联盟
    if (account.getAllianceId() != null) {
      throw new BusinessException("请先退出联盟再删除账号");
    }

    gameAccountRepository.delete(account);
  }

  public List<GameAccount> getUserGameAccounts() {
    Long userId = UserContext.getUserId();
    return gameAccountRepository.findByUserIdOrderByServerIdDesc(userId);
  }

  public GameAccount getGameAccountById(Long accountId) {
    return gameAccountRepository
        .findById(accountId)
        .orElseThrow(() -> new BusinessException("游戏账号不存在"));
  }
}
