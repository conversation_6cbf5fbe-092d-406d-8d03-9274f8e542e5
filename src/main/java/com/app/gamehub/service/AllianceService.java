package com.app.gamehub.service;

import com.app.gamehub.dto.CreateAllianceRequest;
import com.app.gamehub.dto.TransferAllianceRequest;
import com.app.gamehub.dto.UpdateAllianceRequest;
import com.app.gamehub.entity.Alliance;
import com.app.gamehub.entity.GameAccount;
import com.app.gamehub.exception.BusinessException;
import com.app.gamehub.repository.AllianceApplicationRepository;
import com.app.gamehub.repository.AllianceRepository;
import com.app.gamehub.repository.GameAccountRepository;
import com.app.gamehub.repository.UserRepository;
import com.app.gamehub.repository.WarApplicationRepository;
import com.app.gamehub.repository.WarArrangementRepository;
import com.app.gamehub.repository.WarGroupRepository;
import com.app.gamehub.util.AllianceCodeGenerator;
import com.app.gamehub.util.UserContext;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
@RequiredArgsConstructor
public class AllianceService {

  private final AllianceRepository allianceRepository;
  private final UserRepository userRepository;
  private final GameAccountRepository gameAccountRepository;
  private final AllianceApplicationRepository allianceApplicationRepository;
  private final WarApplicationRepository warApplicationRepository;
  private final WarArrangementRepository warArrangementRepository;
  private final WarGroupRepository warGroupRepository;
  private final AllianceCodeGenerator codeGenerator;

  @Transactional
  public Alliance createAlliance(CreateAllianceRequest request) {
    Long userId = UserContext.getUserId();
    // 验证用户是否存在
    if (!userRepository.existsById(userId)) {
      throw new BusinessException("用户不存在");
    }

    // 生成唯一的联盟编码
    String code;
    do {
      code = codeGenerator.generateCode();
    } while (allianceRepository.existsByCode(code));

    Alliance alliance = new Alliance();
    alliance.setName(request.getName());
    alliance.setCode(code);
    alliance.setServerId(request.getServerId());
    alliance.setLeaderId(userId);

    return allianceRepository.save(alliance);
  }

  @Transactional
  public Alliance updateAlliance(Long allianceId, UpdateAllianceRequest request) {
    Alliance alliance =
        allianceRepository.findById(allianceId).orElseThrow(() -> new BusinessException("联盟不存在"));

    // 验证是否为盟主
    if (!alliance.getLeaderId().equals(UserContext.getUserId())) {
      throw new BusinessException("只有盟主可以更新联盟信息");
    }

    if (request.getName() != null && !request.getName().trim().isEmpty()) {
      alliance.setName(request.getName().trim());
    }

    if (request.getCode() != null && !request.getCode().trim().isEmpty()) {
      String newCode = request.getCode().trim().toUpperCase();
      if (!alliance.getCode().equals(newCode)) {
        if (allianceRepository.existsByCode(newCode)) {
          throw new BusinessException("联盟编码已存在");
        }
        alliance.setCode(newCode);
      }
    }

    return allianceRepository.save(alliance);
  }

  @Transactional
  public void deleteAlliance(Long allianceId) {
    Long userId = UserContext.getUserId();
    Alliance alliance =
        allianceRepository.findById(allianceId).orElseThrow(() -> new BusinessException("联盟不存在"));

    // 验证是否为盟主
    if (!alliance.getLeaderId().equals(userId)) {
      throw new BusinessException("只有盟主可以删除联盟");
    }

    log.info("开始删除联盟 ID: {}, 名称: {}", allianceId, alliance.getName());

    // 1. 获取所有联盟成员并移出联盟
    List<GameAccount> members =
        gameAccountRepository.findByAllianceIdOrderByPowerValueDesc(allianceId);
    if (!members.isEmpty()) {
      log.info("移出联盟成员，共 {} 人", members.size());
      for (GameAccount member : members) {
        member.setAllianceId(null);
        member.setMemberTier(null);
        gameAccountRepository.save(member);
      }
    }

    // 2. 删除所有申请加入联盟的数据
    log.info("删除联盟申请数据");
    allianceApplicationRepository.deleteAllByAllianceId(allianceId);

    // 3. 删除所有战事申请数据
    log.info("删除战事申请数据");
    warApplicationRepository.deleteAllByAllianceId(allianceId);

    // 4. 删除所有战事安排数据
    log.info("删除战事安排数据");
    warArrangementRepository.deleteByAllianceId(allianceId);

    // 5. 删除所有战事分组数据
    log.info("删除战事分组数据");
    warGroupRepository.deleteByAllianceId(allianceId);

    // 6. 最后删除联盟本身
    log.info("删除联盟主体数据");
    allianceRepository.delete(alliance);

    log.info("联盟删除完成 ID: {}", allianceId);
  }

  @Transactional
  public Alliance transferAlliance(Long allianceId, TransferAllianceRequest request) {
    Long userId = UserContext.getUserId();
    Alliance alliance =
        allianceRepository.findById(allianceId).orElseThrow(() -> new BusinessException("联盟不存在"));

    // 验证是否为盟主
    if (!alliance.getLeaderId().equals(userId)) {
      throw new BusinessException("只有盟主可以转交联盟");
    }

    Long newLeaderId = request.getNewLeaderId();

    // 验证新盟主是否存在
    if (!userRepository.existsById(newLeaderId)) {
      throw new BusinessException("新盟主用户不存在");
    }

    // 验证新盟主是否为联盟成员
    boolean isNewLeaderMember =
        gameAccountRepository.existsByUserIdAndServerIdAndAllianceIdIsNotNull(
            newLeaderId, alliance.getServerId());
    if (!isNewLeaderMember) {
      throw new BusinessException("新盟主必须是联盟成员");
    }

    alliance.setLeaderId(newLeaderId);
    return allianceRepository.save(alliance);
  }

  public List<Alliance> getUserAlliances() {
    Long userId = UserContext.getUserId();
    return allianceRepository.findByLeaderIdOrderByServerIdDesc(userId);
  }

  public Alliance getAllianceById(Long allianceId) {
    return allianceRepository
        .findById(allianceId)
        .orElseThrow(() -> new BusinessException("联盟不存在"));
  }

  public Alliance getAllianceByCode(String code) {
    return allianceRepository
        .findByCode(code.toUpperCase())
        .orElseThrow(() -> new BusinessException("联盟不存在"));
  }
}
