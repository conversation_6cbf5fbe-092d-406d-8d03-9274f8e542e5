package com.app.gamehub.controller;

import com.app.gamehub.dto.*;
import com.app.gamehub.entity.DynastyPosition;
import com.app.gamehub.entity.PositionGrab;
import com.app.gamehub.entity.PositionType;
import com.app.gamehub.service.PositionGrabService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import java.time.LocalDate;
import org.springframework.web.bind.annotation.*;

/** 官职抢夺控制器 */
@RestController
@RequestMapping("/api/dynasties/{dynastyId}/positions")
@Tag(name = "官职抢夺", description = "官职抢夺相关接口")
public class PositionGrabController {

  private final PositionGrabService positionGrabService;

  public PositionGrabController(PositionGrabService positionGrabService) {
    this.positionGrabService = positionGrabService;
  }

  @PostMapping("/grab-time")
  @Operation(summary = "设置官职抢夺时间")
  public ApiResponse<DynastyPosition> setPositionGrabTime(
      @Parameter(description = "王朝ID", example = "1") @PathVariable Long dynastyId,
      @Valid @RequestBody SetPositionGrabTimeRequest request) {
    DynastyPosition position = positionGrabService.setPositionGrabTime(dynastyId, request);
    return ApiResponse.success("官职抢夺时间设置成功", position);
  }

  @GetMapping("/grab-time")
  @Operation(summary = "查询官职抢夺时间的设置信息")
  public ApiResponse<DynastyPosition> getPositionGrabTime(
      @Parameter(description = "王朝ID", example = "1") @PathVariable Long dynastyId,
      @RequestParam PositionType positionType) {
    DynastyPosition position = positionGrabService.getPositionGrabTime(dynastyId, positionType);
    return ApiResponse.success("查询官职抢夺时间设置", position);
  }

  @PostMapping("/grab")
  @Operation(summary = "抢夺官职")
  public ApiResponse<PositionGrab> grabPosition(
      @Parameter(description = "王朝ID", example = "1") @PathVariable Long dynastyId,
      @Parameter(description = "账号ID", example = "1") @RequestParam Long accountId,
      @Valid @RequestBody GrabPositionRequest request) {
    PositionGrab grab = positionGrabService.grabPosition(dynastyId, accountId, request);
    return ApiResponse.success("官职抢夺成功", grab);
  }

  @GetMapping("/grab-results")
  @Operation(summary = "获取官职抢夺结果")
  public ApiResponse<PositionGrabResultResponse> getGrabResults(
      @Parameter(description = "王朝ID", example = "1") @PathVariable Long dynastyId,
      @Parameter(description = "任职日期", example = "2025/08/12") @RequestParam LocalDate dutyDate) {
    PositionGrabResultResponse results = positionGrabService.getGrabResults(dynastyId, dutyDate);
    return ApiResponse.success(results);
  }
}
