package com.app.gamehub.service;

import com.app.gamehub.dto.GrabPositionRequest;
import com.app.gamehub.entity.*;
import com.app.gamehub.exception.BusinessException;
import com.app.gamehub.repository.*;
import com.app.gamehub.util.UserContext;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class PositionGrabServiceReadWriteLockTest {

    @Mock
    private DynastyRepository dynastyRepository;

    @Mock
    private DynastyPositionRepository dynastyPositionRepository;

    @Mock
    private PositionGrabRepository positionGrabRepository;

    @Mock
    private GameAccountRepository gameAccountRepository;

    @InjectMocks
    private PositionGrabService positionGrabService;

    private Long dynastyId = 1L;
    private Long accountId = 1L;
    private Long userId = 1L;
    private GameAccount account;
    private Dynasty dynasty;
    private DynastyPosition position;

    @BeforeEach
    void setUp() {
        account = new GameAccount();
        account.setId(accountId);
        account.setUserId(userId);
        account.setDynastyId(dynastyId);
        account.setAccountName("TestAccount");

        dynasty = new Dynasty();
        dynasty.setId(dynastyId);
        dynasty.setGrabEnabled(true);

        position = new DynastyPosition();
        position.setDynastyId(dynastyId);
        position.setPositionType(PositionType.TAI_WEI);
        position.setGrabStartTime(LocalDateTime.now().minusHours(1));
        position.setGrabEndTime(LocalDateTime.now().plusHours(1));
        position.setDutyDate(LocalDate.now().plusDays(1));
        position.setDisabledTimeSlots("");
    }

    @Test
    void grabPosition_BasicReadWriteLockFunctionality_ShouldWork() {
        // Given
        GrabPositionRequest request = new GrabPositionRequest();
        request.setPositionType(PositionType.TAI_WEI);
        request.setTimeSlot(10);

        when(gameAccountRepository.findById(accountId)).thenReturn(Optional.of(account));
        when(dynastyRepository.findById(dynastyId)).thenReturn(Optional.of(dynasty));
        when(dynastyPositionRepository.findByDynastyIdAndPositionType(dynastyId, PositionType.TAI_WEI))
                .thenReturn(Optional.of(position));

        // 模拟用户没有重复抢夺
        when(positionGrabRepository.existsByAccountIdAndDynastyIdAndPositionTypeAndDutyDate(
                accountId, dynastyId, PositionType.TAI_WEI, position.getDutyDate()))
                .thenReturn(false);

        // 模拟时段可以抢夺
        when(positionGrabRepository.existsByDynastyIdAndPositionTypeAndDutyDateAndTimeSlot(
                dynastyId, PositionType.TAI_WEI, position.getDutyDate(), 10))
                .thenReturn(false);

        PositionGrab savedGrab = new PositionGrab();
        when(positionGrabRepository.save(any(PositionGrab.class))).thenReturn(savedGrab);

        // When
        try (MockedStatic<UserContext> mockedStatic = mockStatic(UserContext.class)) {
            mockedStatic.when(UserContext::getUserId).thenReturn(userId);
            PositionGrab result = positionGrabService.grabPosition(dynastyId, accountId, request);

            // Then
            assertNotNull(result);
            verify(positionGrabRepository, times(2)).existsByDynastyIdAndPositionTypeAndDutyDateAndTimeSlot(
                    dynastyId, PositionType.TAI_WEI, position.getDutyDate(), 10);
            verify(positionGrabRepository).save(any(PositionGrab.class));
        }
    }

    @Test
    void removeLock_WithValidDynastyIds_ShouldRemoveCorrectLocks() {
        // Given - 先创建一些锁
        try (MockedStatic<UserContext> mockedStatic = mockStatic(UserContext.class)) {
            mockedStatic.when(UserContext::getUserId).thenReturn(userId);

            when(gameAccountRepository.findById(accountId)).thenReturn(Optional.of(account));
            when(dynastyRepository.findById(dynastyId)).thenReturn(Optional.of(dynasty));
            when(dynastyPositionRepository.findByDynastyIdAndPositionType(dynastyId, PositionType.TAI_WEI))
                    .thenReturn(Optional.of(position));
            when(positionGrabRepository.existsByAccountIdAndDynastyIdAndPositionTypeAndDutyDate(any(), any(), any(), any()))
                    .thenReturn(false);
            when(positionGrabRepository.existsByDynastyIdAndPositionTypeAndDutyDateAndTimeSlot(any(), any(), any(), any()))
                    .thenReturn(false);
            when(positionGrabRepository.save(any())).thenReturn(new PositionGrab());

            // 创建一些锁
            GrabPositionRequest request1 = new GrabPositionRequest();
            request1.setPositionType(PositionType.TAI_WEI);
            request1.setTimeSlot(10);

            GrabPositionRequest request2 = new GrabPositionRequest();
            request2.setPositionType(PositionType.TAI_WEI);
            request2.setTimeSlot(11);

            positionGrabService.grabPosition(dynastyId, accountId, request1);
            positionGrabService.grabPosition(dynastyId, accountId, request2);

            // 为另一个王朝创建锁
            Long dynastyId2 = 2L;
            GameAccount account2 = new GameAccount();
            account2.setId(2L);
            account2.setUserId(userId);
            account2.setDynastyId(dynastyId2);

            Dynasty dynasty2 = new Dynasty();
            dynasty2.setId(dynastyId2);
            dynasty2.setGrabEnabled(true);

            DynastyPosition position2 = new DynastyPosition();
            position2.setDynastyId(dynastyId2);
            position2.setPositionType(PositionType.TAI_WEI);
            position2.setGrabStartTime(LocalDateTime.now().minusHours(1));
            position2.setGrabEndTime(LocalDateTime.now().plusHours(1));
            position2.setDutyDate(LocalDate.now().plusDays(1));

            when(gameAccountRepository.findById(2L)).thenReturn(Optional.of(account2));
            when(dynastyRepository.findById(dynastyId2)).thenReturn(Optional.of(dynasty2));
            when(dynastyPositionRepository.findByDynastyIdAndPositionType(dynastyId2, PositionType.TAI_WEI))
                    .thenReturn(Optional.of(position2));

            positionGrabService.grabPosition(dynastyId2, 2L, request1);

            // 验证锁已创建
            assertTrue(positionGrabService.getLockCount() > 0);

            // When - 移除第一个王朝的锁
            int removedCount = positionGrabService.removeLock(Set.of(dynastyId));

            // Then
            assertTrue(removedCount > 0);
            // 第二个王朝的锁应该还在
            assertTrue(positionGrabService.getLockCount() > 0);
        }
    }

    @Test
    void removeLock_WithInvalidLockKey_ShouldHandleGracefully() {
        // Given - 手动添加一个无效的锁键（这在实际情况下不应该发生，但测试异常处理）
        // 由于grabLocks是private的，我们通过正常方式创建锁，然后测试removeLock的健壮性

        // When
        int removedCount = positionGrabService.removeLock(Set.of(999L)); // 不存在的王朝ID

        // Then - 应该不会抛出异常
        assertEquals(0, removedCount);
    }

    @Test
    void removeLock_WithEmptySet_ShouldReturnZero() {
        // When
        int removedCount = positionGrabService.removeLock(Set.of());

        // Then
        assertEquals(0, removedCount);
    }

    @Test
    void removeLock_WithNullSet_ShouldReturnZero() {
        // When
        int removedCount = positionGrabService.removeLock(null);

        // Then
        assertEquals(0, removedCount);
    }
}
