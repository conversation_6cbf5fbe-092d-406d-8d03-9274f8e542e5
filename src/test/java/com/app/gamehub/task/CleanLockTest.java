package com.app.gamehub.task;

import com.app.gamehub.entity.DynastyPosition;
import com.app.gamehub.entity.PositionType;
import com.app.gamehub.repository.DynastyPositionRepository;
import com.app.gamehub.service.PositionGrabService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.Set;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CleanLockTest {

    @Mock
    private PositionGrabService positionGrabService;

    @Mock
    private DynastyPositionRepository dynastyPositionRepository;

    @InjectMocks
    private CleanLock cleanLock;

    private DynastyPosition endedPosition1;
    private DynastyPosition endedPosition2;

    @BeforeEach
    void setUp() {
        endedPosition1 = new DynastyPosition();
        endedPosition1.setDynastyId(1L);
        endedPosition1.setPositionType(PositionType.TAI_WEI);
        endedPosition1.setGrabEndTime(LocalDateTime.now().minusHours(1));

        endedPosition2 = new DynastyPosition();
        endedPosition2.setDynastyId(2L);
        endedPosition2.setPositionType(PositionType.SHANG_SHU_LING);
        endedPosition2.setGrabEndTime(LocalDateTime.now().minusHours(2));
    }

    @Test
    void cleanPositionGrabLock_WithEndedPositions_ShouldRemoveLocks() {
        // Given
        when(dynastyPositionRepository.findByGrabEndTimeBefore(any(LocalDateTime.class)))
                .thenReturn(Arrays.asList(endedPosition1, endedPosition2));
        when(positionGrabService.removeLock(any(Set.class))).thenReturn(5);

        // When
        cleanLock.cleanPositionGrabLock();

        // Then
        verify(dynastyPositionRepository).findByGrabEndTimeBefore(any(LocalDateTime.class));
        verify(positionGrabService).removeLock(eq(Set.of(1L, 2L)));
    }

    @Test
    void cleanPositionGrabLock_WithNoEndedPositions_ShouldNotRemoveLocks() {
        // Given
        when(dynastyPositionRepository.findByGrabEndTimeBefore(any(LocalDateTime.class)))
                .thenReturn(Collections.emptyList());

        // When
        cleanLock.cleanPositionGrabLock();

        // Then
        verify(dynastyPositionRepository).findByGrabEndTimeBefore(any(LocalDateTime.class));
        verify(positionGrabService, never()).removeLock(any());
    }

    @Test
    void cleanPositionGrabLock_WithException_ShouldHandleGracefully() {
        // Given
        when(dynastyPositionRepository.findByGrabEndTimeBefore(any(LocalDateTime.class)))
                .thenThrow(new RuntimeException("Database error"));

        // When & Then - should not throw exception
        cleanLock.cleanPositionGrabLock();

        verify(dynastyPositionRepository).findByGrabEndTimeBefore(any(LocalDateTime.class));
        verify(positionGrabService, never()).removeLock(any());
    }

    @Test
    void cleanPositionGrabLock_WithSameDynastyMultiplePositions_ShouldDeduplicateDynastyIds() {
        // Given
        DynastyPosition position3 = new DynastyPosition();
        position3.setDynastyId(1L); // Same dynasty as endedPosition1
        position3.setPositionType(PositionType.SHANG_SHU_LING);
        position3.setGrabEndTime(LocalDateTime.now().minusHours(3));

        when(dynastyPositionRepository.findByGrabEndTimeBefore(any(LocalDateTime.class)))
                .thenReturn(Arrays.asList(endedPosition1, endedPosition2, position3));
        when(positionGrabService.removeLock(any(Set.class))).thenReturn(3);

        // When
        cleanLock.cleanPositionGrabLock();

        // Then
        verify(positionGrabService).removeLock(eq(Set.of(1L, 2L))); // Should deduplicate dynasty ID 1
    }
}
